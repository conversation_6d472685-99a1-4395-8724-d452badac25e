// Assignment Routes - Guru Digital Pelangi
import express from 'express';
import { PrismaClient } from '@prisma/client';
import { authenticateToken } from '../middleware/auth.js';

const router = express.Router();
const prisma = new PrismaClient();

// GET /api/assignments - Get assignments for current teacher
router.get('/', authenticateToken, async (req, res) => {
  try {
    console.log('Getting assignments for user:', req.user.id);
    const { classId, status, type, search } = req.query;
    const teacherId = req.user.id;

    // Build where clause - only show assignments created by current teacher
    const where = {
      teacherId: teacherId, // ISOLATION: Only show teacher's own assignments
    };

    if (classId) where.classId = classId;
    if (status) where.status = status;
    if (type) where.type = type;
    if (search) {
      where.OR = [
        { title: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } }
      ];
    }

    console.log('Assignment query where:', where);

    const assignments = await prisma.assignment.findMany({
      where,
      include: {
        class: {
          include: {
            subject: true
          }
        },
        teacher: {
          select: {
            id: true,
            fullName: true,
            nip: true
          }
        },
        submissions: {
          include: {
            student: {
              select: {
                id: true,
                fullName: true,
                studentId: true
              }
            }
          }
        }
      },
      orderBy: {
        deadline: 'asc'
      }
    });

    console.log('Found assignments:', assignments.length);

    // Calculate statistics for each assignment
    const assignmentsWithStats = assignments.map(assignment => {
      const totalStudents = assignment.class?.studentCount || 0;
      const submittedCount = assignment.submissions.filter(s => s.status !== 'NOT_SUBMITTED').length;
      const gradedCount = assignment.submissions.filter(s => s.status === 'GRADED').length;
      const lateCount = assignment.submissions.filter(s => s.status === 'LATE_SUBMITTED').length;
      
      // Determine assignment status
      let assignmentStatus = assignment.status;
      if (assignment.status === 'PUBLISHED' && new Date() > new Date(assignment.deadline)) {
        assignmentStatus = 'overdue';
      } else if (assignment.status === 'PUBLISHED') {
        assignmentStatus = 'active';
      } else if (assignment.status === 'CLOSED') {
        assignmentStatus = 'completed';
      }

      return {
        ...assignment,
        submissionsCount: submittedCount,
        totalStudents,
        gradedCount,
        lateCount,
        status: assignmentStatus
      };
    });

    res.json({
      success: true,
      data: assignmentsWithStats
    });

  } catch (error) {
    console.error('Error fetching assignments:', error);
    res.status(500).json({
      success: false,
      error: 'Gagal mengambil data tugas'
    });
  }
});

// GET /api/assignments/stats - Get assignment statistics for current teacher
router.get('/stats', authenticateToken, async (req, res) => {
  try {
    const teacherId = req.user.id;
    const now = new Date();
    const weekFromNow = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000);

    const assignments = await prisma.assignment.findMany({
      where: {
        teacherId: teacherId // Only count teacher's own assignments
      },
      include: {
        submissions: true
      }
    });

    const stats = {
      total: assignments.length,
      active: assignments.filter(a => a.status === 'PUBLISHED' && new Date(a.deadline) > now).length,
      overdue: assignments.filter(a => a.status === 'PUBLISHED' && new Date(a.deadline) <= now).length,
      completed: assignments.filter(a => a.status === 'CLOSED').length,
      thisWeek: assignments.filter(a => {
        const deadline = new Date(a.deadline);
        return deadline >= now && deadline <= weekFromNow;
      }).length,
      averagePoints: assignments.length > 0 
        ? Math.round(assignments.reduce((sum, a) => sum + a.points, 0) / assignments.length)
        : 0
    };

    res.json({
      success: true,
      data: stats
    });

  } catch (error) {
    console.error('Error fetching assignment stats:', error);
    res.status(500).json({
      success: false,
      error: 'Gagal mengambil statistik tugas'
    });
  }
});

// POST /api/assignments - Create new assignment
router.post('/', authenticateToken, async (req, res) => {
  try {
    const {
      title,
      description,
      instructions,
      points = 100,
      deadline,
      type = 'TUGAS_HARIAN',
      classId
    } = req.body;

    const teacherId = req.user.id;

    // Validate required fields
    if (!title || !deadline || !classId) {
      return res.status(400).json({
        success: false,
        error: 'Title, deadline, dan class ID wajib diisi'
      });
    }

    // Check if teacher has access to this class
    const classTeacher = await prisma.classTeacher.findFirst({
      where: {
        classId: classId,
        teacherId: teacherId
      }
    });

    if (!classTeacher) {
      return res.status(403).json({
        success: false,
        error: 'Anda tidak memiliki akses ke kelas ini'
      });
    }

    // Create assignment
    const assignment = await prisma.assignment.create({
      data: {
        title,
        description,
        instructions,
        points: parseInt(points),
        deadline: new Date(deadline),
        type,
        classId,
        teacherId,
        status: 'DRAFT'
      },
      include: {
        class: {
          include: {
            subject: true,
            students: true
          }
        },
        teacher: {
          select: {
            id: true,
            fullName: true,
            nip: true
          }
        }
      }
    });

    // Create submission records for all students in the class
    const students = assignment.class.students;
    if (students.length > 0) {
      await prisma.assignmentSubmission.createMany({
        data: students.map(student => ({
          assignmentId: assignment.id,
          studentId: student.id,
          status: 'NOT_SUBMITTED'
        }))
      });
    }

    res.status(201).json({
      success: true,
      data: assignment,
      message: 'Tugas berhasil dibuat'
    });

  } catch (error) {
    console.error('Error creating assignment:', error);
    res.status(500).json({
      success: false,
      error: 'Gagal membuat tugas'
    });
  }
});

// PUT /api/assignments/:id - Update assignment
router.put('/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    const teacherId = req.user.id;
    const updateData = req.body;

    // Check if assignment exists and belongs to current teacher
    const existingAssignment = await prisma.assignment.findFirst({
      where: {
        id: id,
        teacherId: teacherId // ISOLATION: Only allow updating own assignments
      }
    });

    if (!existingAssignment) {
      return res.status(404).json({
        success: false,
        error: 'Tugas tidak ditemukan atau Anda tidak memiliki akses'
      });
    }

    // Update assignment
    const assignment = await prisma.assignment.update({
      where: { id },
      data: {
        ...updateData,
        deadline: updateData.deadline ? new Date(updateData.deadline) : undefined,
        points: updateData.points ? parseInt(updateData.points) : undefined
      },
      include: {
        class: {
          include: {
            subject: true
          }
        },
        teacher: {
          select: {
            id: true,
            fullName: true,
            nip: true
          }
        }
      }
    });

    res.json({
      success: true,
      data: assignment,
      message: 'Tugas berhasil diupdate'
    });

  } catch (error) {
    console.error('Error updating assignment:', error);
    res.status(500).json({
      success: false,
      error: 'Gagal mengupdate tugas'
    });
  }
});

// DELETE /api/assignments/:id - Delete assignment
router.delete('/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    const teacherId = req.user.id;

    // Check if assignment exists and belongs to current teacher
    const existingAssignment = await prisma.assignment.findFirst({
      where: {
        id: id,
        teacherId: teacherId // ISOLATION: Only allow deleting own assignments
      }
    });

    if (!existingAssignment) {
      return res.status(404).json({
        success: false,
        error: 'Tugas tidak ditemukan atau Anda tidak memiliki akses'
      });
    }

    // Delete assignment (submissions will be deleted automatically due to cascade)
    await prisma.assignment.delete({
      where: { id }
    });

    res.json({
      success: true,
      message: 'Tugas berhasil dihapus'
    });

  } catch (error) {
    console.error('Error deleting assignment:', error);
    res.status(500).json({
      success: false,
      error: 'Gagal menghapus tugas'
    });
  }
});

export default router;
