// Gamification Controller for Guru Digital Pelangi
// Handles XP, badges, challenges, and student achievements

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// Get gamification dashboard data
export const getDashboard = async (req, res) => {
  try {
    // Get total students with XP data
    const totalStudents = await prisma.student.count({
      where: { status: 'ACTIVE' }
    });

    // Get total badges
    const totalBadges = await prisma.badge.count({
      where: { isActive: true }
    });

    // Get active challenges
    const activeChallenges = await prisma.challenge.count({
      where: { 
        isActive: true,
        endDate: { gte: new Date() }
      }
    });

    // Get top students by XP
    const topStudents = await prisma.student.findMany({
      take: 10,
      include: {
        studentXp: true,
        class: true
      },
      orderBy: {
        studentXp: {
          totalXp: 'desc'
        }
      }
    });

    // Get recent achievements
    const recentAchievements = await prisma.studentAchievement.findMany({
      take: 10,
      include: {
        student: {
          include: {
            class: true
          }
        }
      },
      orderBy: {
        earnedAt: 'desc'
      }
    });

    res.json({
      success: true,
      data: {
        stats: {
          totalStudents,
          totalBadges,
          activeChallenges
        },
        topStudents,
        recentAchievements
      }
    });
  } catch (error) {
    console.error('Error getting gamification dashboard:', error);
    res.status(500).json({
      success: false,
      error: 'Gagal mengambil data dashboard gamifikasi'
    });
  }
};

// Get all students with XP data
export const getStudentsWithXP = async (req, res) => {
  try {
    const students = await prisma.student.findMany({
      where: { status: 'ACTIVE' },
      include: {
        studentXp: true,
        class: true,
        studentBadges: {
          include: {
            badge: true
          }
        }
      },
      orderBy: {
        studentXp: {
          totalXp: 'desc'
        }
      }
    });

    res.json({
      success: true,
      data: students
    });
  } catch (error) {
    console.error('Error getting students with XP:', error);
    res.status(500).json({
      success: false,
      error: 'Gagal mengambil data siswa'
    });
  }
};

// Get all badges
export const getBadges = async (req, res) => {
  try {
    const badges = await prisma.badge.findMany({
      where: { isActive: true },
      include: {
        studentBadges: {
          include: {
            student: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    res.json({
      success: true,
      data: badges
    });
  } catch (error) {
    console.error('Error getting badges:', error);
    res.status(500).json({
      success: false,
      error: 'Gagal mengambil data badge'
    });
  }
};

// Create new badge
export const createBadge = async (req, res) => {
  try {
    const { name, description, icon, xpReward, criteria } = req.body;

    if (!name || !description) {
      return res.status(400).json({
        success: false,
        error: 'Nama dan deskripsi badge harus diisi'
      });
    }

    const badge = await prisma.badge.create({
      data: {
        name,
        description,
        icon: icon || '🏆',
        xpReward: parseInt(xpReward) || 0,
        criteria,
        isActive: true
      }
    });

    res.json({
      success: true,
      data: badge,
      message: 'Badge berhasil dibuat'
    });
  } catch (error) {
    console.error('Error creating badge:', error);
    res.status(500).json({
      success: false,
      error: 'Gagal membuat badge'
    });
  }
};

// Get all challenges
export const getChallenges = async (req, res) => {
  try {
    const challenges = await prisma.challenge.findMany({
      include: {
        participations: {
          include: {
            student: true
          }
        },
        createdByUser: {
          select: {
            fullName: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    res.json({
      success: true,
      data: challenges
    });
  } catch (error) {
    console.error('Error getting challenges:', error);
    res.status(500).json({
      success: false,
      error: 'Gagal mengambil data challenge'
    });
  }
};

// Create new challenge
export const createChallenge = async (req, res) => {
  try {
    const { title, description, duration, targetType, xpReward } = req.body;
    const userId = req.user?.id; // From auth middleware

    if (!title || !description) {
      return res.status(400).json({
        success: false,
        error: 'Judul dan deskripsi challenge harus diisi'
      });
    }

    const endDate = new Date();
    endDate.setDate(endDate.getDate() + (duration || 7));

    const challenge = await prisma.challenge.create({
      data: {
        title,
        description,
        duration: duration || 7,
        targetType: targetType || 'ALL_STUDENTS',
        xpReward: parseInt(xpReward) || 0,
        endDate,
        createdBy: userId || 'system',
        isActive: true
      }
    });

    res.json({
      success: true,
      data: challenge,
      message: 'Challenge berhasil dibuat'
    });
  } catch (error) {
    console.error('Error creating challenge:', error);
    res.status(500).json({
      success: false,
      error: 'Gagal membuat challenge'
    });
  }
};

// Give reward to student (XP or Badge)
export const giveRewardToStudent = async (req, res) => {
  try {
    const { studentId, type, xpAmount, badgeId, description } = req.body;
    const userId = req.user?.id; // From auth middleware

    if (!studentId || !type) {
      return res.status(400).json({
        success: false,
        error: 'Student ID dan tipe reward harus diisi'
      });
    }

    // Check if student exists
    const student = await prisma.student.findUnique({
      where: { id: studentId },
      include: { studentXp: true }
    });

    if (!student) {
      return res.status(404).json({
        success: false,
        error: 'Siswa tidak ditemukan'
      });
    }

    if (type === 'xp') {
      // Give XP reward
      const xpToAdd = parseInt(xpAmount) || 0;
      
      if (student.studentXp) {
        // Update existing XP record
        await prisma.studentXp.update({
          where: { studentId },
          data: {
            totalXp: {
              increment: xpToAdd
            }
          }
        });
      } else {
        // Create new XP record
        await prisma.studentXp.create({
          data: {
            studentId,
            totalXp: xpToAdd,
            level: 1,
            levelName: 'Pemula'
          }
        });
      }

      // Create achievement record
      await prisma.studentAchievement.create({
        data: {
          studentId,
          type: 'XP_REWARD',
          title: 'Bonus XP',
          description: description || `Mendapat ${xpToAdd} XP`,
          xpReward: xpToAdd
        }
      });

    } else if (type === 'badge') {
      // Give badge reward
      if (!badgeId) {
        return res.status(400).json({
          success: false,
          error: 'Badge ID harus diisi'
        });
      }

      // Check if badge exists
      const badge = await prisma.badge.findUnique({
        where: { id: badgeId }
      });

      if (!badge) {
        return res.status(404).json({
          success: false,
          error: 'Badge tidak ditemukan'
        });
      }

      // Award badge
      await prisma.studentBadge.create({
        data: {
          studentId,
          badgeId,
          awardedBy: userId,
          reason: description || 'Diberikan oleh guru'
        }
      });
    }

    res.json({
      success: true,
      message: 'Reward berhasil diberikan'
    });
  } catch (error) {
    console.error('Error giving reward:', error);
    res.status(500).json({
      success: false,
      error: 'Gagal memberikan reward'
    });
  }
};
