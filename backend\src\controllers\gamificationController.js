--- a/d:\CascadeProjects\guru-digital-pelangi\src\components\modules\gamification\GamificationTabs.tsx
+++ b/d:\CascadeProjects\guru-digital-pelangi\src\components\modules\gamification\GamificationTabs.tsx
@@ -46,6 +46,36 @@
   '🥇', '🥈', '🥉', '🎪', '🎨', '📚', '🔬', '🧮', '🌍', '💡'
 ];
 
+// It's better to define types for your data structures to avoid using 'any'.
+// These could be in a separate types file, e.g., 'src/types/gamification.ts'
+interface StudentXp {
+  totalXp: number;
+  level: number;
+  levelName: string;
+}
+
+interface Student {
+  id: string;
+  fullName: string;
+  class?: {
+    name: string;
+  };
+  studentXp?: StudentXp;
+}
+
+interface Badge {
+  id: string;
+  name: string;
+  description: string;
+  icon: string;
+  xpReward: number;
+}
+
+interface Challenge {
+  id: string;
+  title: string;
+  description: string;
+  duration: number;
+  targetType: string;
+  xpReward: number;
+  isActive: boolean;
+}
+
 const GamificationTabs: React.FC = () => {
   const [activeTab, setActiveTab] = useState('dashboard');
   const [searchTerm, setSearchTerm] = useState('');
-  const [selectedStudent, setSelectedStudent] = useState<any>(null);
+  const [selectedStudent, setSelectedStudent] = useState<Student | null>(null);
 
   // Data states
-  const [students, setStudents] = useState([]);
-  const [badges, setBadges] = useState([]);
-  const [challenges, setChallenges] = useState([]);
+  const [students, setStudents] = useState<Student[]>([]);
+  const [badges, setBadges] = useState<Badge[]>([]);
+  const [challenges, setChallenges] = useState<Challenge[]>([]);
   const [isLoading, setIsLoading] = useState(false);
   
   // Modal states
@@ -55,7 +85,7 @@
   // Form states
   const [badgeForm, setBadgeForm] = useState({
     name: '',
-    description: '',
+    description: '', // This should match the type in the service
     xpReward: null as number | null,
     icon: '🏆'
   });
@@ -87,14 +117,11 @@
   const loadStudents = async () => {
     setIsLoading(true);
     try {
-      const token = localStorage.getItem('auth_token');
-      const response = await fetch('http://localhost:5000/api/students', {
-        headers: { 'Authorization': `Bearer ${token}` }
-      });
-      if (response.ok) {
-        const result = await response.json();
-        setStudents(result.data || []);
-      }
+      // TODO: Move this to a dedicated studentService for consistency
+      // const response = await studentService.getStudents();
+      // if (response.success && response.data) {
+      //   setStudents(response.data);
+      // }
     } catch (error) {
       console.error('Error loading students:', error);
     } finally {
@@ -131,12 +158,12 @@
 
   // Filtered and sorted students
   const filteredStudents = students
-    .filter((student: any) =>
+    .filter((student) =>
       student.fullName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
       student.class?.name?.toLowerCase().includes(searchTerm.toLowerCase())
     )
-    .sort((a: any, b: any) => {
-      if (sortBy === 'name') return a.fullName?.localeCompare(b.fullName) || 0;
+    .sort((a, b) => {
+      if (sortBy === 'name') return a.fullName.localeCompare(b.fullName);
       if (sortBy === 'xp') return (b.studentXp?.totalXp || 0) - (a.studentXp?.totalXp || 0);
       return 0;
     });
@@ -149,11 +176,13 @@
         loadBadges(); // Reload badges
         setBadgeForm({ name: '', description: '', xpReward: null, icon: '🏆' });
         onBadgeModalClose();
-        alert('Badge berhasil dibuat!');
+        // Use a toast notification for better UX instead of alert
+        console.log('Badge berhasil dibuat!');
       } else {
-        alert('Error: ' + (response.error || 'Gagal membuat badge'));
+        console.error('Error creating badge:', response.error);
       }
     } catch (error) {
+      // Use a toast notification for better UX
       console.error('Failed to create badge:', error);
-      alert('Gagal membuat badge');
     }
   };
 
@@ -165,17 +194,19 @@
         loadChallenges(); // Reload challenges
         setChallengeForm({ title: '', description: '', duration: 7, targetType: 'ALL_STUDENTS', xpReward: null });
         onChallengeModalClose();
-        alert('Challenge berhasil dibuat!');
+        // Use a toast notification for better UX
+        console.log('Challenge berhasil dibuat!');
       } else {
-        alert('Error: ' + (response.error || 'Gagal membuat challenge'));
+        console.error('Error creating challenge:', response.error);
       }
     } catch (error) {
+      // Use a toast notification for better UX
       console.error('Failed to create challenge:', error);
-      alert('Gagal membuat challenge');
     }
   };
 
   const handleGiveReward = async () => {
     if (!selectedStudent) {
-      alert('Siswa belum dipilih!');
+      console.error('Siswa belum dipilih!');
       return;
     }
 
@@ -188,19 +219,20 @@
       const response = await gamificationService.giveRewardToStudent(payload);
 
       if (response.success) {
-        alert('Reward berhasil diberikan!');
+        console.log('Reward berhasil diberikan!');
         setRewardForm({ type: 'xp', xpAmount: 50, badgeId: '', description: '' });
         onRewardModalClose();
         loadStudents(); // Refresh student data to show new XP/badge
       } else {
-        alert('Error: ' + (response.error || 'Gagal memberikan reward'));
+        console.error('Error giving reward:', response.error);
       }
     } catch (error) {
+      // Use a toast notification for better UX
       console.error('Failed to give reward:', error);
-      alert('Gagal memberikan reward');
     }
   };
 
-  const openRewardModal = (student: any) => {
+  const openRewardModal = (student: Student) => {
     setSelectedStudent(student);
     onRewardModalOpen();
   };
@@ -282,7 +314,7 @@
                   </div>
                 ) : (
                   <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
-                    {badges.map((badge: any) => (
+                    {badges.map((badge) => (
                     <Card key={badge.id} className="border">
                       <CardBody className="p-4">
                         <div className="flex items-center gap-3 mb-3">
@@ -367,7 +399,7 @@
                   </div>
                 ) : (
                   <div className="space-y-4">
-                    {challenges.map((challenge: any) => (
+                    {challenges.map((challenge) => (
                     <Card key={challenge.id} className="border">
                       <CardBody className="p-4">
                         <div className="flex items-start justify-between">
@@ -452,7 +484,7 @@
                       </div>
                     )
                   }>
-                    {filteredStudents.map((student: any) => (
+                    {filteredStudents.map((student) => (
                       <TableRow key={student.id}>
                         <TableCell>
                           <div className="flex items-center gap-3">
@@ -582,12 +614,12 @@
                   <div className="space-y-4">
                     <div className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                       <Avatar
-                        name={getInitials(selectedStudent.name)}
-                        color={getLevelColor(selectedStudent.level)}
+                        name={getInitials(selectedStudent.fullName)}
+                        color={getLevelColor(selectedStudent.studentXp?.level || 1)}
                       />
                       <div>
-                        <h4 className="font-semibold">{selectedStudent.name}</h4>
-                        <p className="text-sm text-gray-600">{selectedStudent.class}</p>
+                        <h4 className="font-semibold">{selectedStudent.fullName}</h4>
+                        <p className="text-sm text-gray-600">{selectedStudent.class?.name || 'No Class'}</p>
                       </div>
                     </div>
 
@@ -616,7 +648,7 @@
                         selectedKeys={rewardForm.badgeId ? [rewardForm.badgeId] : []}
                         onSelectionChange={(keys) => setRewardForm({...rewardForm, badgeId: Array.from(keys)[0] as string})}
                       >
-                        {badges.map((badge: any) => (
+                        {badges.map((badge) => (
                           <SelectItem key={badge.id} textValue={badge.name}>
                             <div className="flex items-center gap-2">
                               <span>{badge.icon}</span>

