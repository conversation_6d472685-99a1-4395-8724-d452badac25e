// Gamification Controller for Guru Digital Pelangi
// Handles XP, badges, challenges, and student achievements

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// Get gamification dashboard data
export const getDashboard = async (req, res) => {
  try {
    // Get total students with XP data
    const totalStudents = await prisma.student.count({
      where: { status: 'ACTIVE' }
    });

    // Get total badges
    const totalBadges = await prisma.badge.count({
      where: { isActive: true }
    });

    // Get active challenges
    const activeChallenges = await prisma.challenge.count({
      where: { 
        isActive: true,
        endDate: { gte: new Date() }
      }
    });

    // Get top students by XP
    const topStudents = await prisma.student.findMany({
      take: 10,
      include: {
        studentXp: true,
        class: true
      },
      orderBy: {
        studentXp: {
          totalXp: 'desc'
        }
      }
    });

    // Get recent achievements
    const recentAchievements = await prisma.studentAchievement.findMany({
      take: 10,
      include: {
        student: {
          include: {
            class: true
          }
        }
      },
      orderBy: {
        earnedAt: 'desc'
      }
    });

    res.json({
      success: true,
      data: {
        stats: {
          totalStudents,
          totalBadges,
          activeChallenges
        },
        topStudents,
        recentAchievements
      }
    });
  } catch (error) {
    console.error('Error getting gamification dashboard:', error);
    res.status(500).json({
      success: false,
      error: 'Gagal mengambil data dashboard gamifikasi'
    });
  }
};

// Get all students with XP data
export const getStudentsWithXP = async (req, res) => {
  try {
    const students = await prisma.student.findMany({
      where: { status: 'ACTIVE' },
      include: {
        studentXp: true,
        class: true,
        studentBadges: {
          include: {
            badge: true
          }
        }
      },
      orderBy: {
        studentXp: {
          totalXp: 'desc'
        }
      }
    });

    res.json({
      success: true,
      data: students
    });
  } catch (error) {
    console.error('Error getting students with XP:', error);
    res.status(500).json({
      success: false,
      error: 'Gagal mengambil data siswa'
    });
  }
};

// Get all badges
export const getBadges = async (req, res) => {
  try {
    const badges = await prisma.badge.findMany({
      where: { isActive: true },
      include: {
        studentBadges: {
          include: {
            student: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    res.json({
      success: true,
      data: badges
    });
  } catch (error) {
    console.error('Error getting badges:', error);
    res.status(500).json({
      success: false,
      error: 'Gagal mengambil data badge'
    });
  }
};

// Create new badge
export const createBadge = async (req, res) => {
  try {
    const { name, description, icon, xpReward, criteria } = req.body;

    if (!name || !description) {
      return res.status(400).json({
        success: false,
        error: 'Nama dan deskripsi badge harus diisi'
      });
    }

    const badge = await prisma.badge.create({
      data: {
        name,
        description,
        icon: icon || '🏆',
        xpReward: parseInt(xpReward) || 0,
        criteria,
        isActive: true
      }
    });

    res.json({
      success: true,
      data: badge,
      message: 'Badge berhasil dibuat'
    });
  } catch (error) {
    console.error('Error creating badge:', error);
    res.status(500).json({
      success: false,
      error: 'Gagal membuat badge'
    });
  }
};

// Get all challenges
export const getChallenges = async (req, res) => {
  try {
    const challenges = await prisma.challenge.findMany({
      include: {
        participations: {
          include: {
            student: true
          }
        },
        createdByUser: {
          select: {
            fullName: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    res.json({
      success: true,
      data: challenges
    });
  } catch (error) {
    console.error('Error getting challenges:', error);
    res.status(500).json({
      success: false,
      error: 'Gagal mengambil data challenge'
    });
  }
};

// Create new challenge
export const createChallenge = async (req, res) => {
  try {
    const { title, description, duration, targetType, xpReward } = req.body;
    const userId = req.user?.id; // From auth middleware

    if (!title || !description) {
      return res.status(400).json({
        success: false,
        error: 'Judul dan deskripsi challenge harus diisi'
      });
    }

    const endDate = new Date();
    endDate.setDate(endDate.getDate() + (duration || 7));

    const challenge = await prisma.challenge.create({
      data: {
        title,
        description,
        duration: duration || 7,
        targetType: targetType || 'ALL_STUDENTS',
        xpReward: parseInt(xpReward) || 0,
        endDate,
        createdBy: userId || 'system',
        isActive: true
      }
    });

    res.json({
      success: true,
      data: challenge,
      message: 'Challenge berhasil dibuat'
    });
  } catch (error) {
    console.error('Error creating challenge:', error);
    res.status(500).json({
      success: false,
      error: 'Gagal membuat challenge'
    });
  }
};

// Give reward to student (XP or Badge)
export const giveRewardToStudent = async (req, res) => {
  try {
    const { studentId, type, xpAmount, badgeId, description } = req.body;
    const userId = req.user?.id; // From auth middleware

    if (!studentId || !type) {
      return res.status(400).json({
        success: false,
        error: 'Student ID dan tipe reward harus diisi'
      });
    }

    // Check if student exists
    const student = await prisma.student.findUnique({
      where: { id: studentId },
      include: { studentXp: true }
    });

    if (!student) {
      return res.status(404).json({
        success: false,
        error: 'Siswa tidak ditemukan'
      });
    }

    if (type === 'xp') {
      // Give XP reward
      const xpToAdd = parseInt(xpAmount) || 0;
      
      if (student.studentXp) {
        // Update existing XP record
        await prisma.studentXp.update({
          where: { studentId },
          data: {
            totalXp: {
              increment: xpToAdd
            }
          }
        });
      } else {
        // Create new XP record
        await prisma.studentXp.create({
          data: {
            studentId,
            totalXp: xpToAdd,
            level: 1,
            levelName: 'Pemula'
          }
        });
      }

      // Create achievement record
      await prisma.studentAchievement.create({
        data: {
          studentId,
          type: 'XP_REWARD',
          title: 'Bonus XP',
          description: description || `Mendapat ${xpToAdd} XP`,
          xpReward: xpToAdd
        }
      });

    } else if (type === 'badge') {
      // Give badge reward
      if (!badgeId) {
        return res.status(400).json({
          success: false,
          error: 'Badge ID harus diisi'
        });
      }

      // Check if badge exists
      const badge = await prisma.badge.findUnique({
        where: { id: badgeId }
      });

      if (!badge) {
        return res.status(404).json({
          success: false,
          error: 'Badge tidak ditemukan'
        });
      }

      // Award badge
      await prisma.studentBadge.create({
        data: {
          studentId,
          badgeId,
          awardedBy: userId,
          reason: description || 'Diberikan oleh guru'
        }
      });
    }

    res.json({
      success: true,
      message: 'Reward berhasil diberikan'
    });
  } catch (error) {
    console.error('Error giving reward:', error);
    res.status(500).json({
      success: false,
      error: 'Gagal memberikan reward'
    });
  }
};

// Get gamification settings
export const getGamificationSettings = async (req, res) => {
  try {
    const settings = await prisma.gamificationSettings.findMany({
      where: { isActive: true },
      orderBy: { createdAt: 'desc' }
    });

    res.json({
      success: true,
      data: settings
    });
  } catch (error) {
    console.error('Error getting gamification settings:', error);
    res.status(500).json({
      success: false,
      error: 'Gagal mengambil pengaturan gamifikasi'
    });
  }
};

// Create gamification settings
export const createGamificationSettings = async (req, res) => {
  try {
    const {
      name,
      description,
      xpPerGrade,
      xpAttendanceBonus,
      xpAbsentPenalty,
      levelThresholds
    } = req.body;

    if (!name) {
      return res.status(400).json({
        success: false,
        error: 'Nama pengaturan harus diisi'
      });
    }

    const settings = await prisma.gamificationSettings.create({
      data: {
        name,
        description,
        xpPerGrade: parseInt(xpPerGrade) || 1,
        xpAttendanceBonus: parseInt(xpAttendanceBonus) || 10,
        xpAbsentPenalty: parseInt(xpAbsentPenalty) || 5,
        levelThresholds: levelThresholds || {
          levels: [
            { level: 1, name: 'Pemula', xp: 0 },
            { level: 2, name: 'Berkembang', xp: 100 },
            { level: 3, name: 'Mahir', xp: 300 },
            { level: 4, name: 'Ahli', xp: 600 },
            { level: 5, name: 'Master', xp: 1000 }
          ]
        },
        isActive: true
      }
    });

    res.json({
      success: true,
      data: settings,
      message: 'Pengaturan gamifikasi berhasil dibuat'
    });
  } catch (error) {
    console.error('Error creating gamification settings:', error);
    res.status(500).json({
      success: false,
      error: 'Gagal membuat pengaturan gamifikasi'
    });
  }
};

// Update gamification settings
export const updateGamificationSettings = async (req, res) => {
  try {
    const { id } = req.params;
    const {
      name,
      description,
      xpPerGrade,
      xpAttendanceBonus,
      xpAbsentPenalty,
      levelThresholds,
      isActive
    } = req.body;

    const settings = await prisma.gamificationSettings.update({
      where: { id },
      data: {
        name,
        description,
        xpPerGrade: parseInt(xpPerGrade),
        xpAttendanceBonus: parseInt(xpAttendanceBonus),
        xpAbsentPenalty: parseInt(xpAbsentPenalty),
        levelThresholds,
        isActive
      }
    });

    res.json({
      success: true,
      data: settings,
      message: 'Pengaturan gamifikasi berhasil diperbarui'
    });
  } catch (error) {
    console.error('Error updating gamification settings:', error);
    res.status(500).json({
      success: false,
      error: 'Gagal memperbarui pengaturan gamifikasi'
    });
  }
};

// Get student achievements
export const getStudentAchievements = async (req, res) => {
  try {
    const { studentId } = req.params;

    const achievements = await prisma.studentAchievement.findMany({
      where: studentId ? { studentId } : {},
      include: {
        student: {
          include: {
            class: true
          }
        }
      },
      orderBy: { earnedAt: 'desc' }
    });

    res.json({
      success: true,
      data: achievements
    });
  } catch (error) {
    console.error('Error getting student achievements:', error);
    res.status(500).json({
      success: false,
      error: 'Gagal mengambil pencapaian siswa'
    });
  }
};

// Update student level based on XP
export const updateStudentLevel = async (req, res) => {
  try {
    const { studentId } = req.params;

    // Get student XP
    const studentXp = await prisma.studentXp.findUnique({
      where: { studentId }
    });

    if (!studentXp) {
      return res.status(404).json({
        success: false,
        error: 'Data XP siswa tidak ditemukan'
      });
    }

    // Get gamification settings
    const settings = await prisma.gamificationSettings.findFirst({
      where: { isActive: true }
    });

    if (!settings) {
      return res.status(404).json({
        success: false,
        error: 'Pengaturan gamifikasi tidak ditemukan'
      });
    }

    // Calculate new level based on XP
    const levels = settings.levelThresholds.levels;
    let newLevel = 1;
    let newLevelName = 'Pemula';

    for (let i = levels.length - 1; i >= 0; i--) {
      if (studentXp.totalXp >= levels[i].xp) {
        newLevel = levels[i].level;
        newLevelName = levels[i].name;
        break;
      }
    }

    // Update student level
    const updatedXp = await prisma.studentXp.update({
      where: { studentId },
      data: {
        level: newLevel,
        levelName: newLevelName
      }
    });

    res.json({
      success: true,
      data: updatedXp,
      message: 'Level siswa berhasil diperbarui'
    });
  } catch (error) {
    console.error('Error updating student level:', error);
    res.status(500).json({
      success: false,
      error: 'Gagal memperbarui level siswa'
    });
  }
};

// Get student XP
export const getStudentXp = async (req, res) => {
  try {
    const { studentId } = req.params;

    const studentXp = await prisma.studentXp.findUnique({
      where: { studentId },
      include: {
        student: {
          include: {
            class: true
          }
        }
      }
    });

    if (!studentXp) {
      return res.status(404).json({
        success: false,
        error: 'Data XP siswa tidak ditemukan'
      });
    }

    res.json({
      success: true,
      data: studentXp
    });
  } catch (error) {
    console.error('Error getting student XP:', error);
    res.status(500).json({
      success: false,
      error: 'Gagal mengambil data XP siswa'
    });
  }
};

// Get class leaderboard
export const getClassLeaderboard = async (req, res) => {
  try {
    const { classId } = req.params;
    const { limit = 20 } = req.query;

    const students = await prisma.student.findMany({
      where: {
        classId,
        status: 'ACTIVE'
      },
      include: {
        studentXp: true,
        class: true
      },
      orderBy: {
        studentXp: {
          totalXp: 'desc'
        }
      },
      take: parseInt(limit)
    });

    // Add rank to each student
    const leaderboard = students.map((student, index) => ({
      rank: index + 1,
      student: {
        id: student.id,
        fullName: student.fullName,
        studentId: student.studentId
      },
      totalXp: student.studentXp?.totalXp || 0,
      level: student.studentXp?.level || 1,
      levelName: student.studentXp?.levelName || 'Pemula',
      attendanceStreak: student.studentXp?.attendanceStreak || 0,
      assignmentStreak: student.studentXp?.assignmentStreak || 0
    }));

    res.json({
      success: true,
      data: leaderboard
    });
  } catch (error) {
    console.error('Error getting class leaderboard:', error);
    res.status(500).json({
      success: false,
      error: 'Gagal mengambil leaderboard kelas'
    });
  }
};

// Award achievement to student
export const awardAchievement = async (req, res) => {
  try {
    const { studentId, type, title, description, xpReward } = req.body;

    if (!studentId || !type || !title) {
      return res.status(400).json({
        success: false,
        error: 'Student ID, tipe, dan judul achievement harus diisi'
      });
    }

    // Create achievement
    const achievement = await prisma.studentAchievement.create({
      data: {
        studentId,
        type,
        title,
        description,
        xpReward: parseInt(xpReward) || 0
      }
    });

    // Add XP if specified
    if (xpReward && xpReward > 0) {
      const studentXp = await prisma.studentXp.findUnique({
        where: { studentId }
      });

      if (studentXp) {
        await prisma.studentXp.update({
          where: { studentId },
          data: {
            totalXp: {
              increment: parseInt(xpReward)
            }
          }
        });
      } else {
        await prisma.studentXp.create({
          data: {
            studentId,
            totalXp: parseInt(xpReward),
            level: 1,
            levelName: 'Pemula'
          }
        });
      }
    }

    res.json({
      success: true,
      data: achievement,
      message: 'Achievement berhasil diberikan'
    });
  } catch (error) {
    console.error('Error awarding achievement:', error);
    res.status(500).json({
      success: false,
      error: 'Gagal memberikan achievement'
    });
  }
};

// Alias for giveRewardToStudent to match route import
export const giveReward = giveRewardToStudent;
