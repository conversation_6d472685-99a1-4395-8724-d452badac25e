import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function main() {
  console.log(`🌱 Mulai seeding database...`);

  // --- Create a default School ---
  const school = await prisma.school.upsert({
    where: { id: 'clw5w4q1g00001234abcd' },
    update: {},
    create: {
      id: 'clw5w4q1g00001234abcd',
      name: 'SMA Digital Pelangi',
      address: 'Jl. Pendidikan No. 123, Jakarta Selatan',
      phone: '021-12345678',
      email: '<EMAIL>',
    },
  });
  console.log(`✅ Created school: ${school.name}`);

  // --- Create Admin User ---
  const hashedAdminPassword = await bcrypt.hash('admin123', 10);
  const admin = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      fullName: 'Admin Utama',
      nip: 'ADMIN001',
      password: hashedAdminPassword,
      role: 'ADMIN',
      status: 'ACTIVE',
    },
  });
  console.log(`✅ Created admin user: ${admin.fullName}`);

  // --- Create Subjects ---
  const subjects = [
    { name: 'Matematika', code: 'MTK', description: 'Mata pelajaran Matematika untuk SMA' },
    { name: 'Bahasa Indonesia', code: 'BIN', description: 'Mata pelajaran Bahasa Indonesia' },
    { name: 'Bahasa Inggris', code: 'ENG', description: 'Mata pelajaran Bahasa Inggris' },
    { name: 'Fisika', code: 'FIS', description: 'Mata pelajaran Fisika' },
    { name: 'Kimia', code: 'KIM', description: 'Mata pelajaran Kimia' },
    { name: 'Biologi', code: 'BIO', description: 'Mata pelajaran Biologi' },
    { name: 'Sejarah', code: 'SEJ', description: 'Mata pelajaran Sejarah' },
    { name: 'Geografi', code: 'GEO', description: 'Mata pelajaran Geografi' },
    { name: 'Ekonomi', code: 'EKO', description: 'Mata pelajaran Ekonomi' },
    { name: 'Sosiologi', code: 'SOS', description: 'Mata pelajaran Sosiologi' },
    { name: 'PJOK', code: 'PJOK', description: 'Pendidikan Jasmani, Olahraga, dan Kesehatan' },
    { name: 'Seni Budaya', code: 'SBK', description: 'Mata pelajaran Seni Budaya' },
  ];

  const createdSubjects: any[] = [];
  for (const subject of subjects) {
    const createdSubject = await prisma.subject.upsert({
      where: { code: subject.code },
      update: {},
      create: subject,
    });
    createdSubjects.push(createdSubject);
  }
  console.log(`✅ Created ${createdSubjects.length} subjects`);

  // --- Create Teacher Users ---
  const hashedTeacherPassword = await bcrypt.hash('guru123', 10);
  const teachers = [
    { email: '<EMAIL>', fullName: 'Budi Santoso', nip: 'GURU001' },
    { email: '<EMAIL>', fullName: 'Siti Nurhaliza', nip: 'GURU002' },
    { email: '<EMAIL>', fullName: 'John Anderson', nip: 'GURU003' },
    { email: '<EMAIL>', fullName: 'Dr. Ahmad Wijaya', nip: 'GURU004' },
    { email: '<EMAIL>', fullName: 'Dewi Sartika', nip: 'GURU005' },
  ];

  const createdTeachers: any[] = [];
  for (const teacher of teachers) {
    const createdTeacher = await prisma.user.upsert({
      where: { email: teacher.email },
      update: {},
      create: {
        ...teacher,
        password: hashedTeacherPassword,
        role: 'GURU',
        status: 'ACTIVE',
      },
    });
    createdTeachers.push(createdTeacher);
  }
  console.log(`✅ Created ${createdTeachers.length} teacher users`);

  // --- Create Classes ---
  const classes = [
    { 
      name: 'X IPA 1', 
      subjectId: createdSubjects.find(s => s.code === 'MTK')?.id,
      description: 'Kelas 10 IPA 1 - Matematika',
      gradeLevel: '10',
      schoolId: school.id,
      studentCount: 30
    },
    { 
      name: 'X IPA 2', 
      subjectId: createdSubjects.find(s => s.code === 'FIS')?.id,
      description: 'Kelas 10 IPA 2 - Fisika',
      gradeLevel: '10',
      schoolId: school.id,
      studentCount: 28
    },
    { 
      name: 'XI IPA 1', 
      subjectId: createdSubjects.find(s => s.code === 'KIM')?.id,
      description: 'Kelas 11 IPA 1 - Kimia',
      gradeLevel: '11',
      schoolId: school.id,
      studentCount: 32
    },
    { 
      name: 'XII IPA 1', 
      subjectId: createdSubjects.find(s => s.code === 'BIO')?.id,
      description: 'Kelas 12 IPA 1 - Biologi',
      gradeLevel: '12',
      schoolId: school.id,
      studentCount: 25
    },
  ];

  const createdClasses: any[] = [];
  for (const classData of classes) {
    const createdClass = await prisma.class.create({
      data: classData,
    });
    createdClasses.push(createdClass);
  }
  console.log(`✅ Created ${createdClasses.length} classes`);

  // --- Assign Teachers to Classes ---
  const classTeacherAssignments = [
    { classId: createdClasses[0].id, teacherId: createdTeachers[0].id }, // Matematika
    { classId: createdClasses[1].id, teacherId: createdTeachers[3].id }, // Fisika
    { classId: createdClasses[2].id, teacherId: createdTeachers[4].id }, // Kimia
    { classId: createdClasses[3].id, teacherId: createdTeachers[1].id }, // Biologi (Siti)
  ];

  for (const assignment of classTeacherAssignments) {
    await prisma.classTeacher.upsert({
      where: {
        classId_teacherId: {
          classId: assignment.classId,
          teacherId: assignment.teacherId,
        },
      },
      update: {},
      create: assignment,
    });
  }
  console.log(`✅ Assigned teachers to classes`);

  // --- Create Sample Students ---
  const students = [
    { studentId: 'NISN001', fullName: 'Andi Pratama', email: '<EMAIL>', classId: createdClasses[0].id, gender: 'L' },
    { studentId: 'NISN002', fullName: 'Sari Indah', email: '<EMAIL>', classId: createdClasses[0].id, gender: 'P' },
    { studentId: 'NISN003', fullName: 'Budi Setiawan', email: '<EMAIL>', classId: createdClasses[1].id, gender: 'L' },
    { studentId: 'NISN004', fullName: 'Maya Sari', email: '<EMAIL>', classId: createdClasses[1].id, gender: 'P' },
    { studentId: 'NISN005', fullName: 'Rudi Hartono', email: '<EMAIL>', classId: createdClasses[2].id, gender: 'L' },
  ];

  const createdStudents: any[] = [];
  for (const student of students) {
    const createdStudent = await prisma.student.create({
      data: {
        ...student,
        dateOfBirth: new Date('2006-01-15'),
        address: 'Jakarta',
        phone: '08123456789',
        parentName: 'Orang Tua',
        parentPhone: '08198765432',
        status: 'ACTIVE',
      },
    });
    createdStudents.push(createdStudent);
  }
  console.log(`✅ Created ${createdStudents.length} students`);

  // --- Create Student XP Records ---
  for (const student of createdStudents) {
    await prisma.studentXp.create({
      data: {
        studentId: student.id,
        totalXp: Math.floor(Math.random() * 500) + 100, // Random XP between 100-600
        level: Math.floor(Math.random() * 3) + 1, // Level 1-3
        levelName: ['Pemula', 'Berkembang', 'Mahir'][Math.floor(Math.random() * 3)],
        attendanceStreak: Math.floor(Math.random() * 10),
        assignmentStreak: Math.floor(Math.random() * 5),
      },
    });
  }
  console.log(`✅ Created student XP records`);

  // --- Create Badges ---
  const badges = [
    { name: 'Rajin Belajar', description: 'Menyelesaikan 10 tugas tepat waktu', icon: '📚', xpReward: 100, schoolId: school.id },
    { name: 'Bintang Kelas', description: 'Mendapatkan nilai rata-rata di atas 90', icon: '⭐', xpReward: 250, schoolId: school.id },
    { name: 'Absen Sempurna', description: 'Hadir 100% dalam sebulan', icon: '🎯', xpReward: 150, schoolId: school.id },
    { name: 'Sang Juara', description: 'Peringkat 1 di kelas', icon: '🏆', xpReward: 500, schoolId: school.id },
    { name: 'Penolong Teman', description: 'Aktif membantu teman', icon: '🤝', xpReward: 75, schoolId: school.id },
    { name: 'Kreatif', description: 'Mengumpulkan tugas dengan kreativitas tinggi', icon: '🎨', xpReward: 125, schoolId: school.id },
    { name: 'Disiplin', description: 'Tidak pernah terlambat selama sebulan', icon: '⏰', xpReward: 100, schoolId: school.id },
  ];

  const createdBadges: any[] = [];
  for (const badge of badges) {
    const createdBadge = await prisma.badge.create({
      data: badge,
    });
    createdBadges.push(createdBadge);
  }
  console.log(`✅ Created ${createdBadges.length} badges`);

  // --- Create Gamification Settings ---
  const gamificationSettings = await prisma.gamificationSettings.upsert({
    where: { name: 'Default Settings' },
    update: {},
    create: {
      name: 'Default Settings',
      description: 'Pengaturan gamifikasi standar untuk SMA Digital Pelangi',
      xpPerGrade: 1,
      xpAttendanceBonus: 10,
      xpAbsentPenalty: 5,
      levelThresholds: {
        levels: [
          { level: 1, name: 'Pemula', xp: 0 },
          { level: 2, name: 'Berkembang', xp: 100 },
          { level: 3, name: 'Mahir', xp: 300 },
          { level: 4, name: 'Ahli', xp: 600 },
          { level: 5, name: 'Master', xp: 1000 },
          { level: 6, name: 'Grandmaster', xp: 1500 },
          { level: 7, name: 'Legend', xp: 2000 },
          { level: 8, name: 'Mythic', xp: 2500 },
          { level: 9, name: 'Immortal', xp: 3000 },
          { level: 10, name: 'Divine', xp: 4000 },
        ]
      },
    },
  });
  console.log(`✅ Created gamification settings: ${gamificationSettings.name}`);

  // --- Create Sample Assignments ---
  const assignments = [
    {
      title: 'Latihan Soal Aljabar',
      description: 'Kerjakan soal-soal aljabar pada buku halaman 45-50',
      instructions: 'Kerjakan dengan rapi dan tunjukkan langkah-langkah penyelesaian',
      points: 100,
      deadline: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
      status: 'PUBLISHED',
      type: 'TUGAS_HARIAN',
      classId: createdClasses[0].id,
      teacherId: createdTeachers[0].id,
    },
    {
      title: 'Quiz Fisika - Gerak Lurus',
      description: 'Quiz tentang materi gerak lurus beraturan dan berubah beraturan',
      instructions: 'Jawab semua pertanyaan dengan benar',
      points: 50,
      deadline: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000), // 3 days from now
      status: 'PUBLISHED',
      type: 'QUIZ',
      classId: createdClasses[1].id,
      teacherId: createdTeachers[3].id,
    },
  ];

  const createdAssignments: any[] = [];
  for (const assignment of assignments) {
    const createdAssignment = await prisma.assignment.create({
      data: assignment,
    });
    createdAssignments.push(createdAssignment);
  }
  console.log(`✅ Created ${createdAssignments.length} assignments`);

  // --- Create Sample Grades ---
  const sampleGrades = [
    { studentId: createdStudents[0].id, subjectId: createdSubjects[0].id, classId: createdClasses[0].id, gradeType: 'TUGAS_HARIAN', score: 85, maxScore: 100 },
    { studentId: createdStudents[1].id, subjectId: createdSubjects[0].id, classId: createdClasses[0].id, gradeType: 'TUGAS_HARIAN', score: 92, maxScore: 100 },
    { studentId: createdStudents[2].id, subjectId: createdSubjects[3].id, classId: createdClasses[1].id, gradeType: 'QUIZ', score: 78, maxScore: 100 },
    { studentId: createdStudents[3].id, subjectId: createdSubjects[3].id, classId: createdClasses[1].id, gradeType: 'QUIZ', score: 88, maxScore: 100 },
  ];

  for (const grade of sampleGrades) {
    await prisma.grade.create({
      data: {
        ...grade,
        description: `Nilai ${grade.gradeType.toLowerCase().replace('_', ' ')}`,
        date: new Date(),
        createdBy: createdTeachers[0].id,
      },
    });
  }
  console.log(`✅ Created sample grades`);

  console.log(`🎉 Seeding selesai! Database berhasil diisi dengan data sample.`);
}

main()
  .catch((e) => {
    console.error('❌ Error during seeding:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
